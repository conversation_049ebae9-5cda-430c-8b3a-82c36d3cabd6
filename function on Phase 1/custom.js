$(document).ready(function(){
    var getUrlParameter = function getUrlParameter(sParam) {
        var sPageURL = window.location.search.substring(1),
            sURLVariables = sPageURL.split('&'),
            sParameterName,
            i;
    
        for (i = 0; i < sURLVariables.length; i++) {
            sParameterName = sURLVariables[i].split('=');
    
            if (sParameterName[0] === sParam) {
                return sParameterName[1] === undefined ? true : decodeURIComponent(sParameterName[1]);
            }
        }
        return false;
    };
    //If URL contains "mystring"
    var booking = getUrlParameter('booking');
    if( booking ){
        $('.bookTestDriveBtn')[0].click();
        
        if( booking == 'Passat Elegance' || booking == 'Passat R-Line' || booking == 'Tiguan Allspace Elegance' || booking == 'Tiguan Allspace R-Line' || booking == 'Arteon R-Line 4MOTION' || booking == 'Golf GTI 2.0TSI' ){
            $('#modalInterest-bookADrive option[value="'+booking+'"]').prop('selected', true);
            $('#modalInterest-bookADrive').next().addClass('focus');
        }
    }

});