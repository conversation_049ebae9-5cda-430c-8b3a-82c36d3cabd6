<link rel="stylesheet" href="form/formStyle.css<?php echo '?v='.time();?>">

<!-- Book A Test Drive -->
<div class="modal fade" id="book-a-test-drive" tabindex="-1" role="dialog" aria-labelledby="book-a-test-drive"
	aria-hidden="true">
	<div class="modal-dialog modal-dialog-centered" role="document">
		<div class="modal-content">
			<div class="defaultForm-header">
				<div class="normal-container bold font14">
					<div class="row">
						<div class="col-12 bold font14">
							<div class="logoBar">
								<div class="logoRow">
									<div class="logo-container">
										<a class="logoLink" href="https://www.audi.com.my/" style="cursor: pointer">
										<img src="img/logo-audi-default-black.svg" class="img-responsive" alt="Audi Book a Test Drive">
										</a>
									</div>
								</div>
							</div>
							<div class="d-flex modal-close" data-dismiss="modal" aria-label="Close" style="cursor: pointer;">
								<div class="modal-close-cross"></div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="defaultForm-body">
				<div class="d-flex">
					<div class="defaultForm-bodyImg">
						<img src="img/audi-schedule-a-test-drive-v2.jpg" alt="Audi Book a Test Drive"/>
					</div>
					<div style="width: 76%"></div>
					<div class="defaultForm-bodyInput">
						<div class="regular extended font60 fontSize34" style="margin-top: -25px;">Book a <span class="bold">test drive</span></div>
						<form>
							<div class="form-group">
								<div class="row">
									<div class="col-md-6">
										<select class="selectedStated remove" id="selectedStated-bookADrive">
											<option selected style="display: none;"></option>
											<option value="Johor" data-select="Johor">Johor</option>
											<option value="Melaka" data-select="Melaka">Melaka</option>
											<option value="Pahang" data-select="Pahang">Pahang</option>
											<option value="Penang" data-select="Penang">Penang</option>
											<option value="Selangor" data-select="Selangor">Selangor</option>
										</select>
										<div class="placehoder">
											Selected state<sup>*</sup>
										</div>
										<div class="anchor">
											<img src="img/form-option-icon.png">
										</div>
										<div class="tick">
											<img src="img/form-tick-icon.png">
										</div>
									</div>
									<div class="col-md-6">
										<input type="text" class="form-control" id="preferableDate-bookADrive"
											aria-describedby="preferableDate-signUp">
										<div class="placehoder">
											Preferred date<sup>*</sup>
										</div>
										<div class="tick">
											<img src="img/form-tick-icon.png">
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-12">
										<select class="selectedDealer remove notactive" id="selectedDealer-bookADrive">
										</select>
										<div class="placehoder">
											Which dealership would you like to visit?<sup>*</sup>
										</div>
										<div class="anchor">
											<img src="img/form-option-icon.png">
										</div>
										<div class="tick">
											<img src="img/form-tick-icon.png">
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-12">
										<select class="modalInterest remove" id="modalInterest-bookADrive">
										<option selected style="display: none;"></option>
										<option value="Q7">Q7</option>
										<option value="A5 Sportback">A5 Sportback</option>
										<option value="e-tron GT quattro">e-tron GT quattro</option>
										<option value="Q3 Sportback">Q3 Sportback</option>
										<!-- <option value="Q5">Q5</option> -->
										<option value="Q8 e-tron">Q8 e-tron</option>
										<option value="Q8 Sportback e-tron">Q8 Sportback e-tron</option>
										<option value="A3 Sedan">A3 Sedan</option>
										<!-- <option value="RS 4 Avant">RS 4 Avant</option> -->
										<!-- <option value="RS 5 Sportback">RS 5 Sportback</option> -->
										<!-- <option value="RS 7 Sportback">RS 7 Sportback</option> -->
										<!-- <option value="Q2">Q2</option> -->
										<!-- <option value="Q3">Q3</option> -->
										<!-- <option value="Q5 Sportback">Q5 Sportback</option> -->
										
										
										</select>
										<div class="placehoder">
											Please choose a model<sup>*</sup>
										</div>
										<div class="anchor">
											<img src="img/form-option-icon.png">
										</div>
										<div class="tick">
											<img src="img/form-tick-icon.png">
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-12">
										<input type="text" class="form-control" id="fullName-bookADrive" aria-describedby="fullName">
										<div class="placehoder">
											Full name<sup>*</sup>
										</div>
										<div class="tick">
											<img src="img/form-tick-icon.png">
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-12">
										<input type="text" class="form-control" id="contactNumber-bookADrive" aria-describedby="contactNumber" onkeypress="return isNumberKey(event);">
										<div class="placehoder">
											Contact number<sup>*</sup>
										</div>
										<div class="tick">
											<img src="img/form-tick-icon.png">
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-12">
										<input type="text" class="form-control" id="email-bookADrive" aria-describedby="email" name="email">
										<div class="placehoder">
											Email<sup>*</sup>
										</div>
										<div class="tick">
											<img src="img/form-tick-icon.png">
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-12" style="margin-bottom: 50px;">
										<div class="regular font16" style="color: #95a6ad">Preferred contact method</div>
										<div class="form-check form-check-inline font24 regular">
											<input class="form-check-input radio-bookADrive" type="radio" name="inlineRadioOptions" id="inlineRadio1" value="Email">
											<label class="form-check-label" for="inlineRadio1" style="margin-left: 8px;">Email</label>
										</div>
										<div class="form-check form-check-inline font24 regular">
											<input class="form-check-input radio-bookADrive" type="radio" name="inlineRadioOptions" id="inlineRadio2" value="Phone">
											<label class="form-check-label" for="inlineRadio2" style="margin-left: 8px;">Phone</label>
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-12">
										<table class="defaultFormTable">
											<tr>
												<td>
													<div class="form-check form-check-inline">
														<input class="form-check-input" type="checkbox"
															id="inlineCheckbox1-bookADrive" checked>
													</div>
												</td>
												<td class="font16 regular" style="color: #95a6ad">I agree for PHS Automotive Malaysia Sdn. Bhd. (PHSAM) and Volkswagen Passenger Cars Malaysia Sdn. Bhd. (VPCM) – a member of the Volkswagen Group -  to fulfil my information request. I affirm that I have read, understood, and agree to the policies in <a href="https://www.audi.com.my/privacy-policy" style="color:#212529;text-decoration:underline;" class="regular bold">PHSAM’s</a> and <a href="https://www.volkswagen.com.my/imprint/data-protection" style="color:#212529;text-decoration:underline;" class="regular bold">VPCM’s</a> Privacy Notice and consent to my personal data being processed and used by PHSAM and VPCM as stipulated in the said Privacy Notice.</td>
											</tr>
										</table>
									</div>
								</div>
								<div class="row">
									<div class="col-12 mt-4">
										<div class="d-flex special mobile-text-center" style="align-items: center;">
											<button class="sendRequestBtn light font16"
												id="sendRequest-bookTestDrive">Submit</button>

											<div class="loader"></div>
										</div>
										<div class="regular font14" id="errorMessage-bookADrive">Invalid Form. Please
											make sure the required field have a tick icon</div>
										<div class="regular font14" id="sucessMessage-bookADrive">Thank you for your
											submission!</div>
									</div>
								</div>
								<input type="hidden" name="hidden-value" id="hidden-value" value="" />
								<?php echo '<input id="input-csrf" type="hidden" name="csrf" value="'.$csrf->getToken().'" />'; ?>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>


<script src="js/form.js<?php echo '?v='.time();?>"></script>