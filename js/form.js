$(document).ready(function () {
  $("input").focus(function () {
    $(this).siblings(".placehoder").addClass("focus");

    $(this).removeClass("error");
    $(this).siblings(".placehoder").removeClass("error");
    $(this).siblings(".anchor").find("img").removeClass("error");
  });
  $("input").focusout(function () {
    getVal = $(this).val();
    if (getVal) {
      $(this).siblings(".tick").addClass("show");
      $(this).addClass("confirm");
    } else {
      $(this).removeClass("confirm");
      $(this).siblings(".placehoder").removeClass("focus");
      $(this).siblings(".tick").removeClass("show");

      $(this).removeClass("error");
      $(this).siblings(".placehoder").removeClass("error");
      $(this).siblings(".anchor").find("img").removeClass("error");
    }
  });

  var pattern = /^\b[A-Z0-9._%-]+@[A-Z0-9.-]+\.[A-Z]{2,4}\b$/i;
  $("input[name*='emai']").focusout(function () {
    getVal = $(this).val();

    if (getVal) {
      if (!pattern.test(getVal)) {
        $(this).siblings(".tick").removeClass("show");
        $(this).removeClass("confirm");
      } else {
        $(this).siblings(".tick").addClass("show");
        $(this).addClass("confirm");
      }
    } else {
      $(this).removeClass("confirm");
      $(this).siblings(".placehoder").removeClass("focus");
      $(this).siblings(".tick").removeClass("show");
    }
  });

  $("#contactNumber-bookADrive").focusout(function () {
    getVal = $(this).val();
    console.log(getVal.length);

    if (getVal) {
      if (getVal.length >= 10 && getVal.length <= 12) {
        $(this).siblings(".tick").addClass("show");
        $(this).addClass("confirm");
      } else {
        $(this).siblings(".tick").removeClass("show");
        $(this).removeClass("confirm");
      }
    } else {
      $(this).removeClass("confirm");
      $(this).siblings(".placehoder").removeClass("focus");
      $(this).siblings(".tick").removeClass("show");
    }
  });

  $("select").change(function () {
    $(this).siblings(".placehoder").addClass("focus");

    $(this).removeClass("error");
    $(this).siblings(".placehoder").removeClass("error");
    $(this).siblings(".anchor").find("img").removeClass("error");

    getVal = $(this).val();
    if (getVal) {
      $(this).siblings(".anchor").addClass("hide");
      $(this).siblings(".tick").addClass("show");
      $(this).addClass("confirm");
    } else {
      $(this).siblings(".placehoder").removeClass("focus");
      $(this).siblings(".tick").removeClass("show");
      $(this).siblings(".anchor").removeClass("hide");
    }
  });

  // BOOK A TEST DRIVE
  // Datepicker Start Date Add Date
  var someDate = new Date();
  // someDate.setDate(someDate.getDate() + 1);

  $("#preferableDate-bookADrive")
    .datepicker({
      autoclose: true,
      format: "dd M yyyy",
      startDate: someDate,
    })
    .on("change", function () {
      $("#preferableDate-bookADrive").siblings(".placehoder").addClass("focus");
      $("#preferableDate-bookADrive").siblings(".tick").addClass("show");
      $("#preferableDate-bookADrive").addClass("confirm");
    })
    .on("focus", function () {
      $(this).trigger("blur");
    });
  $("#selectedStated-bookADrive").change(function () {
    dataSelect = $("option:selected", this).attr("data-select");

    // $("#selectedDealer-bookADrive > option").each(function() {
    //     $("#selectedDealer-bookADrive").val('');
    //     $("#selectedDealer-bookADrive").siblings('.placehoder').removeClass('focus');
    //     $("#selectedDealer-bookADrive").siblings('.tick').removeClass('show');
    //     $("#selectedDealer-bookADrive").siblings('.anchor').removeClass('hide');

    //     dataSelectInner = $(this).attr('data-select');
    //     $(this).show();

    //     if(dataSelectInner != dataSelect)
    //     {
    //         $(this).hide();
    //     }
    // });

    $("#selectedDealer-bookADrive").empty();
    $.each(dealerArray, function (index) {
      if (dealerArray[index].state == dataSelect) {
        $("#selectedDealer-bookADrive").append(
          '<option selected style="display: none;"></option>'
        );
        $.each(dealerArray[index].dealer, function (index, data) {
          $("#selectedDealer-bookADrive").append(data.option);
        });
      }
    });

    $("#selectedDealer-bookADrive").removeClass("notactive");
    $("#selectedDealer-bookADrive").css("opacity", "1");
    $("#selectedDealer-bookADrive").removeClass("confirm");
    $("#selectedDealer-bookADrive")
      .siblings(".placehoder")
      .removeClass("focus");
    $("#selectedDealer-bookADrive").siblings(".tick").removeClass("show");
  });

  // Handle dealer selection change to filter car models
  $("#selectedDealer-bookADrive").change(function () {
    var selectedDealerText = $("option:selected", this).attr("data-dealer");

    // Reset car model selection
    $("#modalInterest-bookADrive").empty();
    $("#modalInterest-bookADrive").append('<option selected style="display: none;"></option>');

    // Reset car model dropdown state
    $("#modalInterest-bookADrive").removeClass("confirm");
    $("#modalInterest-bookADrive").siblings(".placehoder").removeClass("focus");
    $("#modalInterest-bookADrive").siblings(".tick").removeClass("show");
    $("#modalInterest-bookADrive").siblings(".anchor").removeClass("hide");

    // Add available car models for selected dealer
    if (selectedDealerText && dealerCarModels[selectedDealerText]) {
      $.each(dealerCarModels[selectedDealerText], function(index, model) {
        $("#modalInterest-bookADrive").append('<option value="' + model + '">' + model + '</option>');
      });

      // Enable car model dropdown
      $("#modalInterest-bookADrive").removeClass("notactive");
      $("#modalInterest-bookADrive").css("opacity", "1");

      // Check if there's a pre-selected car model from slider
      var preselectedModel = $("#modalInterest-bookADrive").attr("data-preselected");
      if (preselectedModel && dealerCarModels[selectedDealerText].includes(preselectedModel)) {
        $("#modalInterest-bookADrive").val(preselectedModel);
        $("#modalInterest-bookADrive").siblings(".placehoder").addClass("focus");
        $("#modalInterest-bookADrive").siblings(".tick").addClass("show");
        $("#modalInterest-bookADrive").addClass("confirm");
        $("#modalInterest-bookADrive").siblings(".anchor").addClass("hide");
        // Clear the preselected data
        $("#modalInterest-bookADrive").removeAttr("data-preselected");
      }
    } else {
      // Disable car model dropdown if no dealer selected
      $("#modalInterest-bookADrive").addClass("notactive");
      $("#modalInterest-bookADrive").css("opacity", "0.3");
    }
  });

  // Handle Test Drive button clicks from car slider
  $(".moreFeatureBtn").click(function(e) {
    e.preventDefault();

    var selectedCarModel = $(this).attr("data-value");

    // Find dealers that have this car model
    var availableDealers = [];
    var availableStates = [];

    $.each(dealerCarModels, function(dealerName, models) {
      if (models.includes(selectedCarModel)) {
        // Find the state for this dealer
        $.each(dealerArray, function(index, stateData) {
          $.each(stateData.dealer, function(dealerIndex, dealerData) {
            if (dealerData.option.includes('data-dealer="' + dealerName + '"')) {
              if (!availableStates.includes(stateData.state)) {
                availableStates.push(stateData.state);
              }
              availableDealers.push({
                dealer: dealerName,
                state: stateData.state,
                option: dealerData.option
              });
            }
          });
        });
      }
    });

    // Open the modal
    $('#book-a-test-drive').modal('show');

    // Reset form
    $("#selectedStated-bookADrive").val('').removeClass("confirm");
    $("#selectedStated-bookADrive").siblings(".placehoder").removeClass("focus");
    $("#selectedStated-bookADrive").siblings(".tick").removeClass("show");

    $("#selectedDealer-bookADrive").empty().addClass("notactive").css("opacity", "0.3");
    $("#selectedDealer-bookADrive").removeClass("confirm");
    $("#selectedDealer-bookADrive").siblings(".placehoder").removeClass("focus");
    $("#selectedDealer-bookADrive").siblings(".tick").removeClass("show");

    $("#modalInterest-bookADrive").empty().addClass("notactive").css("opacity", "0.3");
    $("#modalInterest-bookADrive").removeClass("confirm");
    $("#modalInterest-bookADrive").siblings(".placehoder").removeClass("focus");
    $("#modalInterest-bookADrive").siblings(".tick").removeClass("show");
    $("#modalInterest-bookADrive").siblings(".anchor").removeClass("hide");

    // If only one state has dealers with this model, pre-select it
    if (availableStates.length === 1) {
      $("#selectedStated-bookADrive").val(availableStates[0]);
      $("#selectedStated-bookADrive").siblings(".placehoder").addClass("focus");
      $("#selectedStated-bookADrive").siblings(".tick").addClass("show");
      $("#selectedStated-bookADrive").addClass("confirm");

      // Populate dealers for this state
      $("#selectedDealer-bookADrive").empty();
      $("#selectedDealer-bookADrive").append('<option selected style="display: none;"></option>');

      $.each(availableDealers, function(index, dealerInfo) {
        if (dealerInfo.state === availableStates[0]) {
          $("#selectedDealer-bookADrive").append(dealerInfo.option);
        }
      });

      $("#selectedDealer-bookADrive").removeClass("notactive");
      $("#selectedDealer-bookADrive").css("opacity", "1");

      // If only one dealer, pre-select it
      var dealersInState = availableDealers.filter(d => d.state === availableStates[0]);
      if (dealersInState.length === 1) {
        $("#selectedDealer-bookADrive option:eq(1)").prop('selected', true);
        $("#selectedDealer-bookADrive").siblings(".placehoder").addClass("focus");
        $("#selectedDealer-bookADrive").siblings(".tick").addClass("show");
        $("#selectedDealer-bookADrive").addClass("confirm");
        $("#selectedDealer-bookADrive").siblings(".anchor").addClass("hide");

        // Populate car models and pre-select the chosen one
        $("#modalInterest-bookADrive").empty();
        $("#modalInterest-bookADrive").append('<option selected style="display: none;"></option>');

        var dealerName = dealersInState[0].dealer;
        $.each(dealerCarModels[dealerName], function(index, model) {
          $("#modalInterest-bookADrive").append('<option value="' + model + '">' + model + '</option>');
        });

        $("#modalInterest-bookADrive").removeClass("notactive");
        $("#modalInterest-bookADrive").css("opacity", "1");

        // Pre-select the car model
        $("#modalInterest-bookADrive").val(selectedCarModel);
        $("#modalInterest-bookADrive").siblings(".placehoder").addClass("focus");
        $("#modalInterest-bookADrive").siblings(".tick").addClass("show");
        $("#modalInterest-bookADrive").addClass("confirm");
        $("#modalInterest-bookADrive").siblings(".anchor").addClass("hide");
      }
    }

    // Store the selected car model for later use
    $("#modalInterest-bookADrive").attr("data-preselected", selectedCarModel);
  });

  var setRadioValueBTD = "Any",
    setPolicyBTD = "agree",
    setNewsletterBTD = "no";
  $(".radio-bookADrive").change(function () {
    setRadioValueBTD = $(this).val();
  });
  $("#sendRequest-bookTestDrive").click(function (e) {
    e.preventDefault();
    $("#errorMessage-bookADrive").removeClass("show");
    $("#errorMessage-bookADrive").show();
    var hidden_value = $("#hidden-value").val();

    if (!$("#selectedStated-bookADrive").hasClass("confirm")) {
      $("#selectedStated-bookADrive").addClass("error");
      $("#selectedStated-bookADrive").siblings(".placehoder").addClass("error");
      $("#selectedStated-bookADrive")
        .siblings(".anchor")
        .find("img")
        .addClass("error");
    }
    if (!$("#preferableDate-bookADrive").hasClass("confirm")) {
      $("#preferableDate-bookADrive").addClass("error");
      $("#preferableDate-bookADrive").siblings(".placehoder").addClass("error");
      $("#preferableDate-bookADrive")
        .siblings(".anchor")
        .find("img")
        .addClass("error");
    }
    if (!$("#selectedDealer-bookADrive").hasClass("confirm")) {
      $("#selectedDealer-bookADrive").addClass("error");
      $("#selectedDealer-bookADrive").siblings(".placehoder").addClass("error");
      $("#selectedDealer-bookADrive")
        .siblings(".anchor")
        .find("img")
        .addClass("error");
    }
    if (!$("#modalInterest-bookADrive").hasClass("confirm")) {
      $("#modalInterest-bookADrive").addClass("error");
      $("#modalInterest-bookADrive").siblings(".placehoder").addClass("error");
      $("#modalInterest-bookADrive")
        .siblings(".anchor")
        .find("img")
        .addClass("error");
    }
    if (!$("#fullName-bookADrive").hasClass("confirm")) {
      $("#fullName-bookADrive").addClass("error");
      $("#fullName-bookADrive").siblings(".placehoder").addClass("error");
      $("#fullName-bookADrive")
        .siblings(".anchor")
        .find("img")
        .addClass("error");
    }
    if (!$("#contactNumber-bookADrive").hasClass("confirm")) {
      $("#contactNumber-bookADrive").addClass("error");
      $("#contactNumber-bookADrive").siblings(".placehoder").addClass("error");
      $("#contactNumber-bookADrive")
        .siblings(".anchor")
        .find("img")
        .addClass("error");
    }
    if (!$("#email-bookADrive").hasClass("confirm")) {
      $("#email-bookADrive").addClass("error");
      $("#email-bookADrive").siblings(".placehoder").addClass("error");
      $("#email-bookADrive").siblings(".anchor").find("img").addClass("error");
    }

    selectedStated = $("#selectedStated-bookADrive").val();
    preferableDate = $("#preferableDate-bookADrive").val();
    selectedDealer = $("#selectedDealer-bookADrive").val();
    modalInterest = $("#modalInterest-bookADrive").val();
    fullName = $("#fullName-bookADrive").val();
    contactNumber = $("#contactNumber-bookADrive").val();
    email = $("#email-bookADrive").val();
    if (
      $("#selectedStated-bookADrive").hasClass("confirm") &&
      $("#preferableDate-bookADrive").hasClass("confirm") &&
      $("#selectedDealer-bookADrive").hasClass("confirm") &&
      $("#modalInterest-bookADrive").hasClass("confirm") &&
      $("#fullName-bookADrive").hasClass("confirm") &&
      $("#contactNumber-bookADrive").hasClass("confirm") && $("#email-bookADrive").hasClass("confirm") ) {
      if ($("input#inlineCheckbox2-bookADrive").is(":checked")) {
        setNewsletterBTD = "yes";
      } else {
        setNewsletterBTD = "no";
      }

      if (!$("input#inlineCheckbox1-bookADrive").is(":checked")) {
        $("#errorMessage-bookADrive").addClass("show");
        $("#errorMessage-bookADrive").text("Please tick the Privacy Notice");
      } else {
        $("#errorMessage-bookADrive").removeClass("show");
        $("#errorMessage-bookADrive").hide();

        // CHECK DATA
        /*
        console.log("Book a test drive");
        console.log(
          selectedStated + ' - ' +
            preferableDate + ' - ' +
            selectedDealer + ' - ' +
            modalInterest + ' - ' +
            fullName + ' - ' +
            contactNumber + ' - ' +
            email
        );
        console.log('Email = '+setRadioValueBTD + ', Agreement = ' + setPolicyBTD + ', Newsletter = ' + setNewsletterBTD);
        */
          $.ajax({
            url: "https://hooks.zapier.com/hooks/catch/3403698/bw7193u",
            data: {
              Source: "website",
              Form: "Book a test drive",
              Campaign: "",
              State: selectedStated,
              "Preferable Date": preferableDate,
              Remarks: "Preferable Date: " + preferableDate,
              "Preferable Dealer": selectedDealer,
              "Car Modal": modalInterest,
              Name: fullName,
              Phone: contactNumber,
              Email: email,
              "Contact Via": setRadioValueBTD,
              "Privacy Policy": setPolicyBTD,
              "Secret Key": "BATD-LandingPage",
            },
            type: "POST",
            beforeSend: function () {
              $(".loader").addClass("show");
              $("#sendRequest-bookTestDrive").text("Sending");

              $("#sendRequest-bookTestDrive").addClass("notactive");
              $("#book-a-test-drive input").addClass("notactive");
              $("#book-a-test-drive select").addClass("notactive");

              $("#book-a-test-drive input").css("opacity", ".3");
              $("#book-a-test-drive select").css("opacity", ".3");
            },
            success: function (result) {
              csrf = $("#input-csrf").val();
              console.log(csrf);

              $.ajax({
                url: "NODNISMemail.php",
                type: "POST",
                data: {
                  State: selectedStated,
                  "Preferable Date": preferableDate,
                  "Preferable Dealer": selectedDealer,
                  CarModal: modalInterest,
                  Name: fullName,
                  Phone: contactNumber,
                  Email: email,
                  "Contact Via": setRadioValueBTD,
                  "Privacy Policy": setPolicyBTD,
                  Newsletter: setNewsletterBTD,
                  csrf: csrf,
                },
                success: function (t) {
                  $.redirect("thank-you.php");
                },
              });
            },
          });
      }
    } else {
      showErrorMessageBAD();
    }
  });

  // SIGN UP FORM
  var setRadioValueSU = "Any",
    setPolicySU = "agree";
  $(".radio-signUp").change(function () {
    setRadioValueSU = $(this).val();

    $(".setError").removeClass("error");
  });
  $("#sendRequest-signUp").click(function (e) {
    e.preventDefault();
    $("#errorMessage-signUp").removeClass("show");
    $("#errorMessage-signUp").show();

    if (setRadioValueSU == "" || setRadioValueSU == "Any") {
      showErrorMessageSU();

      $(".setError").addClass("error");
    } else if (!$("#fullName-signUp").hasClass("confirm")) {
      showErrorMessageSU();

      $("#fullName-signUp").addClass("error");
      $("#fullName-signUp").siblings(".placehoder").addClass("error");
      $("#fullName-signUp").siblings(".anchor").find("img").addClass("error");
    } else if (!$("#email-signUp").hasClass("confirm")) {
      showErrorMessageSU();

      $("#email-signUp").addClass("error");
      $("#email-signUp").siblings(".placehoder").addClass("error");
      $("#email-signUp").siblings(".anchor").find("img").addClass("error");
    } else {
      title = $("#title-signUp").val();
      fullName = $("#fullName-signUp").val();
      email = $("#email-signUp").val();

      if (!$("input#inlineCheckbox1-signUp").is(":checked")) {
        $("#errorMessage-signUp").addClass("show");
        $("#errorMessage-signUp").text("Please tick the Privacy Notice");
      } else {
        $("#errorMessage-signUp").removeClass("show");
        $("#errorMessage-signUp").hide();

        // CHECK DATA
        console.log("Sign Up");
        console.log(title + fullName + email);
        console.log(setRadioValueSU + setPolicySU);

        $.ajax({
          url: "https://hooks.zapier.com/hooks/catch/3403698/opzx997",
          data: {
            Website: "Book a test drive",
            Form: "Sign Up",
            Campaign: "Facebook",
            Salutation: setRadioValueSU,
            Title: title,
            Name: fullName,
            Email: email,
            "Privacy Policy": setPolicySU,
            "Secret Key": "O5TmVNTd4^q5",
          },
          beforeSend: function () {
            $(".loader").addClass("show");
            $("#sendRequest-signUp").text("Subscribing");

            $("#sendRequest-signUp").addClass("notactive");
            $("#sign-up-form input").addClass("notactive");
            $("#sign-up-form select").addClass("notactive");

            $("#sign-up-form input").css("opacity", ".3");
            $("#sign-up-form select").css("opacity", ".3");
          },
          type: "POST",
          success: function (result) {
            $.redirect("thank-you.php");
          },
        });
      }
    }
  });

    // LINK FORM
    var getUrlParameter = function getUrlParameter(sParam) {
      var sPageURL = window.location.search.substring(1),
          sURLVariables = sPageURL.split('&'),
          sParameterName,
          i;
  
      for (i = 0; i < sURLVariables.length; i++) {
          sParameterName = sURLVariables[i].split('=');
  
          if (sParameterName[0] === sParam) {
              return sParameterName[1] === undefined ? true : decodeURIComponent(sParameterName[1]);
          }
      }
      return false;
  
  };
  var modelInterest = getUrlParameter('modelInterest');
  
  if( modelInterest != ' ' || modelInterest != '' ){
    if( modelInterest == 'Passat Elegance' || modelInterest == 'Passat R-Line' || modelInterest == 'Tiguan Allspace Elegance' || modelInterest == 'Tiguan Allspace R-Line' || modelInterest == 'Arteon R-Line 4MOTION' || modelInterest == 'Golf GTI 2.0TSI' ){
      $('.bookTestDriveBtn').click();
      $("#modalInterest-bookADrive option[value='"+modelInterest+"']").attr('selected', 'selected');
      setTimeout(function(){
        $('#modalInterest-bookADrive').next().addClass('focus');
      }, 250);
    }
  }
});
